/**
 * Animations CSS
 * This file contains non-critical animations for the entire application
 * It's loaded after the initial render to improve performance
 */

/* Fade in animation */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scale in animation */
@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Slide in animations */
@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Rotate animations */
@keyframes rotate-in {
  from {
    opacity: 0;
    transform: rotate(-5deg);
  }
  to {
    opacity: 1;
    transform: rotate(0);
  }
}

@keyframes rotate-360 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Pulse animation */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Bounce animation */
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

/* Shake animation */
@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* Flip animations */
@keyframes flip-x {
  from {
    transform: perspective(400px) rotateX(90deg);
    opacity: 0;
  }
  to {
    transform: perspective(400px) rotateX(0deg);
    opacity: 1;
  }
}

@keyframes flip-y {
  from {
    transform: perspective(400px) rotateY(90deg);
    opacity: 0;
  }
  to {
    transform: perspective(400px) rotateY(0deg);
    opacity: 1;
  }
}

/* Utility classes for animations */
.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out forwards;
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out forwards;
}

.animate-slide-in-up {
  animation: slide-in-up 0.5s ease-out forwards;
}

.animate-slide-in-down {
  animation: slide-in-down 0.5s ease-out forwards;
}

.animate-rotate-in {
  animation: rotate-in 0.5s ease-out forwards;
}

.animate-rotate-360 {
  animation: rotate-360 2s linear infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-bounce {
  animation: bounce 2s ease infinite;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-flip-x {
  animation: flip-x 0.5s ease-out forwards;
}

.animate-flip-y {
  animation: flip-y 0.5s ease-out forwards;
}

/* Animation delay utilities */
.delay-100 {
  animation-delay: 100ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

.delay-1000 {
  animation-delay: 1000ms;
}

/* Animation duration utilities */
.duration-300 {
  animation-duration: 300ms;
}

.duration-500 {
  animation-duration: 500ms;
}

.duration-1000 {
  animation-duration: 1000ms;
}

.duration-2000 {
  animation-duration: 2000ms;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in,
  .animate-scale-in,
  .animate-slide-in-left,
  .animate-slide-in-right,
  .animate-slide-in-up,
  .animate-slide-in-down,
  .animate-rotate-in,
  .animate-rotate-360,
  .animate-pulse,
  .animate-bounce,
  .animate-shake,
  .animate-flip-x,
  .animate-flip-y {
    animation: none !important;
    transition: none !important;
  }
}
